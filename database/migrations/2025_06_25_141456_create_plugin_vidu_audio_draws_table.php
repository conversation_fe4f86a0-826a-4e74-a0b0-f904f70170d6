<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_vidu_audio_draws', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('no')->comment('订单号');
            $table->string('task_id')->nullable()->comment('任务ID');
            $table->string('model')->default('audio1.0')->comment('模型名称');
            $table->float('duration')->default(10)->comment('音频时长(秒)');
            $table->string('type')->nullable()->comment('音频时长(秒)');
            $table->json('timing_prompts')->comment('可控音效参数');
            $table->integer('seed')->nullable()->comment('随机种子');
            $table->json('res')->nullable()->comment('响应结果');
            $table->tinyInteger('status')->default(0)->comment('状态');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->integer('score')->default(0)->comment('消耗积分');
            $table->string('audio_url')->nullable()->comment('生成的音频地址');
            $table->timestamp('start_at')->nullable()->comment('开始时间');
            $table->timestamp('over_at')->nullable()->comment('完成时间');
            $table->boolean('is_asset')->default(false)->comment('是否来自助理');
            $table->timestamps();
            $table->softDeletes();

            $table->index('no');
            $table->index('task_id');
            $table->index('user_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_vidu_audio_draws');
    }
};
