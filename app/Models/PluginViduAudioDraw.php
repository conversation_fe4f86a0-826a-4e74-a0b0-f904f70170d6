<?php

namespace App\Models;

use App\Events\ViduAudioDrawStatusChangedEvent;
use App\Exceptions\ValidatorException;
use App\Jobs\Vidu\ViduAudioQueryJob;
use App\Models\Traits\AssetTrait;
use App\Packages\ViDu\ViDu;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;

class PluginViduAudioDraw extends Model
{
    use BelongsToUser,
        AutoCreateOrderNo,
        HasCovers,
        AssetTrait,
        SoftDeletes;

    const SCORE_AUDIO = 5; // 音效生成使用的积分

    // 状态常量
    const STATUS_INIT    = AiUnifyAsset::STATUS_INIT;
    const STATUS_ING     = AiUnifyAsset::STATUS_ING;
    const STATUS_SUCCESS = AiUnifyAsset::STATUS_SUCCESS;
    const STATUS_ERROR   = AiUnifyAsset::STATUS_ERROR;

    const TYPE_TIMING_AUDIO = 'timing_audio';
    const TYPE_MAP          = [
        self::TYPE_TIMING_AUDIO => '可控文生音效'
    ];

    public string $orderNoPrefix = 'TA'; // Timing Audio 订单号前缀

    protected $casts = [
        'timing_prompts' => 'json',
        'res'            => 'json',
        'over_at'        => 'datetime',
        'start_at'       => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        self::creating(function (PluginViduAudioDraw $model) {
            // 计算积分
            $model->score = self::SCORE_AUDIO;

            $user = request()->kernel?->guestUser();
            if (! $user) {
                throw new ValidatorException('用户尚未登录');
            }

            if ($model->score > 0 && $user->account->score < $model->score) {
                throw new ValidatorException('积分不足');
            }
        });

        self::updated(function (PluginViduAudioDraw $model) {
            if ($model->wasChanged('status')) {
                event(new ViduAudioDrawStatusChangedEvent(
                    $model,
                    $model->status
                ));
            }
        });
    }

    public function getMediaTypeAttribute(): string
    {
        return 'audio';
    }

    public function getTypeTextAttribute(): string
    {
        return self::TYPE_MAP[$this->type] ?? '未知类型';
    }

    public function getStatusTextAttribute()
    {
        return AiUnifyAsset::STATUS[$this->status] ?? '';
    }

    public function draw()
    {
        if ($this->canDraw()) {
            if ($this->score > 0) {
                $this->user->aiSpend(
                    ruleName: 'create_work',
                    model: $this,
                    score: -$this->score,
                    source: [
                        'remark' => '创作内容【'.$this->type_text.'】',
                    ],
                );
            }
            $this->setIng();
            try {
                $this->drawAudio();
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        } else {
            throw new Exception('当前状态不可执行');
        }
    }

    public function canDraw(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_INIT ||
            $this->status == AiUnifyAsset::STATUS_ERROR;
    }

    public function canPolling(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_ING;
    }

    public function setIng()
    {
        $this->status   = AiUnifyAsset::STATUS_ING;
        $this->start_at = now();
        $this->save();
    }

    public function drawAudio()
    {
        $result = ViDu::audio()->createTimingAudioTask($this);
        if ($result->isSuccess()) {
            $this->task_id = $result->task_id;
            $this->save();
            $this->jobQuery();
        } else {
            Log::info('生成可控音效错误', $result->toArray());
            $this->error($result->getMessage());
            throw new Exception($result->getMessage());
        }
    }

    public function jobQuery()
    {
        ViduAudioQueryJob::dispatch($this)->delay(now()->addSeconds(3));
    }

    public function error(string $message)
    {
        $this->error_message = $message;
        $this->status        = self::STATUS_ERROR;
        $this->save();
    }

    public function audioQuery()
    {
        $result = ViDu::audio()->queryTimingAudioTask($this->task_id);

        if ($result->isSuccess()) {
            if (in_array($result->state, [
                'created',
                'queueing',
                'processing',
            ])) {
                $this->jobQuery();
                return;
            }

            Log::info('查询可控音效任务状态', $result->toArray());

            if ($result->state === 'success') {
                $audio_url = $result->creations[0]['url'] ?? null;

                if ($audio_url) {
                    // 保存音频文件
                    $audioInfo       = $this->saveStorage($audio_url);
                    $this->audio_url = $audioInfo;
                }

                $this->status        = self::STATUS_SUCCESS;
                $this->over_at       = now();
                $this->res           = [
                    'audio_url' => $audio_url,
                ];
                $this->error_message = null;
                $this->save();
            } elseif ($result->state === 'failed') {
                $this->error($result->getMessage() ?? '生成失败');
            }
        } else {
            $this->error($result->getMessage());
        }
    }

    protected function getOssPath(): string
    {
        return 'vidu/audio/'.date('Y/m/d').'/';
    }
}
