<?php

namespace App\Http\Resources\Vidu;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TemplateResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'                => $this->id,
            'category'          => new CategoryResource($this->category),
            'name'              => $this->name,
            'scene'             => $this->scene,
            'detail'            => $this->getDetail(),
            'input_instruction' => $this->getInputInstruction(),
            'instructions'      => $this->instructions,
            'video_url'         => $this->video_url,
            'cover_url'         => $this->cover_url,
            'prompt'            => $this->prompt_zh?$this->prompt_zh:$this->prompt,
            'extra'             => $this->getExtraParams(),
            'extra_title'       => $this->getExtraTitle(),
            'created_at'        => (string) $this->created_at,
        ];
    }
}