<?php

namespace App\Http\Resources\Vidu;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ViduAudioDrawResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'             => $this->id,
            'no'             => $this->no,
            'task_id'        => $this->task_id,
            'model'          => $this->model,
            'duration'       => $this->duration,
            'timing_prompts' => $this->timing_prompts,
            'seed'           => $this->seed,
            'audio_url'      => $this->audio_url,
            'status'         => $this->status,
            'status_text'    => $this->status_text,
            'type_text'      => $this->type_text,
            'media_type'     => $this->media_type,
            'score'          => $this->score,
            'error_message'  => $this->error_message,
            'res'            => $this->res,
            'start_at'       => $this->start_at?->format('Y-m-d H:i:s'),
            'over_at'        => $this->over_at?->format('Y-m-d H:i:s'),
            'created_at'     => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
