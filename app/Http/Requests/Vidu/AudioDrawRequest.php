<?php

namespace App\Http\Requests\Vidu;

use Illuminate\Foundation\Http\FormRequest;

class AudioDrawRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'model' => 'nullable|string|in:audio1.0',
            'duration' => 'nullable|numeric|min:2|max:10',
            'timing_prompts' => 'required|array|min:1',
            'timing_prompts.*.from' => 'required|numeric|min:0',
            'timing_prompts.*.to' => 'required|numeric|min:0',
            'timing_prompts.*.prompt' => 'required|string|max:1500',
            'seed' => 'nullable|integer|min:0',
            'is_asset' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'model.in' => '模型名称只能是 audio1.0',
            'duration.numeric' => '音频时长必须是数字',
            'duration.min' => '音频时长不能小于2秒',
            'duration.max' => '音频时长不能大于10秒',
            'timing_prompts.required' => '音效事件参数不能为空',
            'timing_prompts.array' => '音效事件参数必须是数组',
            'timing_prompts.min' => '至少需要一个音效事件',
            'timing_prompts.*.from.required' => '音效事件开始时间不能为空',
            'timing_prompts.*.from.numeric' => '音效事件开始时间必须是数字',
            'timing_prompts.*.from.min' => '音效事件开始时间不能小于0',
            'timing_prompts.*.to.required' => '音效事件结束时间不能为空',
            'timing_prompts.*.to.numeric' => '音效事件结束时间必须是数字',
            'timing_prompts.*.to.min' => '音效事件结束时间不能小于0',
            'timing_prompts.*.prompt.required' => '音效事件描述不能为空',
            'timing_prompts.*.prompt.max' => '音效事件描述不能超过1500个字符',
            'seed.integer' => '随机种子必须是整数',
            'seed.min' => '随机种子不能小于0',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $duration = $this->input('duration', 10);
            $timingPrompts = $this->input('timing_prompts', []);

            foreach ($timingPrompts as $index => $prompt) {
                $from = $prompt['from'] ?? 0;
                $to = $prompt['to'] ?? 0;

                // 检查 from 是否小于 to
                if ($from >= $to) {
                    $validator->errors()->add(
                        "timing_prompts.{$index}.to",
                        '结束时间必须大于开始时间'
                    );
                }

                // 检查时间是否在 duration 范围内
                if ($from > $duration) {
                    $validator->errors()->add(
                        "timing_prompts.{$index}.from",
                        '开始时间不能超过音频总时长'
                    );
                }

                if ($to > $duration) {
                    $validator->errors()->add(
                        "timing_prompts.{$index}.to",
                        '结束时间不能超过音频总时长'
                    );
                }
            }
        });
    }
}
