<?php

namespace App\Packages\ViDu\Audio;

use App\Models\PluginViduAudioDraw;
use App\Packages\ViDu\BaseClient;

class Client extends BaseClient
{
    /**
     * Notes: 创建可控文生音效任务
     *
     * @Author: Claude 4.0 sonnet
     * @Date: 2025/6/25
     * @param  PluginViduAudioDraw  $audioDraw
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function createTimingAudioTask(PluginViduAudioDraw $audioDraw)
    {
        $path = '/ent/v2/timing2audio';
        
        $data = [
            'model' => $audioDraw->model,
            'duration' => $audioDraw->duration,
            'timing_prompts' => $audioDraw->timing_prompts,
        ];

        if ($audioDraw->seed) {
            $data['seed'] = $audioDraw->seed;
        }

        $this->params = $data;
        return $this->post($path);
    }

    /**
     * Notes: 查询可控文生音效任务状态
     *
     * @Author: Claude 4.0 sonnet
     * @Date: 2025/6/25
     * @param  string  $taskId
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function queryTimingAudioTask(string $taskId)
    {
        $path = "/ent/v2/tasks/{$taskId}/creations";
        return $this->get($path);
    }

    /**
     * Notes: 取消可控文生音效任务
     *
     * @Author: Claude 4.0 sonnet
     * @Date: 2025/6/25
     * @param  string  $taskId
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function cancelTimingAudioTask(string $taskId)
    {
        $path = "/ent/v2/tasks/{$taskId}/cancel";
        return $this->post($path);
    }
}
