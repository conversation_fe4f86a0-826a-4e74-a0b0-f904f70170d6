<?php

namespace App\Packages\ViDu\Task;

use App\Models\PluginViduDraw;
use App\Packages\ViDu\BaseClient;

class Client extends BaseClient
{
    /**
     * Notes: 根据模板生成视频
     *
     * @Author: 玄尘
     * @Date: 2025/4/15 17:11
     * @param $template
     * @param $images
     */
    public function createTask(PluginViduDraw $draw)
    {
        $path   = '/ent/v2/template2video';
        $inputs = $draw->inputs[0];
        $prompt = $inputs['prompt'];
        if (! $prompt) {
            $prompt = $draw->template->prompt;
        }
        $seed = 0;
        if (isset($inputs['seed'])) {
            $seed = $inputs['seed'];
        }
        $data = [
            'template' => $draw->template->scene,
            'images'   => $inputs['url'],
            'prompt'   => $prompt,
        ];
        if ($seed) {
            $data['seed'] = $seed;
        }
        if (isset($inputs['aspect_ratio'])) {
            $data['aspect_ratio'] = $inputs['aspect_ratio'];
        }
        if (isset($inputs['area'])) {
            $data['area'] = $inputs['area'];
        }
        if (isset($inputs['beast'])) {
            $data['beast'] = $inputs['beast'];
        }
        $this->params = $data;
        return $this->post($path);
    }

    /**
     * Notes: 取消任务
     *
     * @Author: 玄尘
     * @Date: 2025/4/21 13:20
     * @param  string  $taskId
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function cancel(string $taskId)
    {
        $path = "/ent/v2/tasks/{$taskId}/cancel";
        return $this->post($path);
    }

    /**
     * Notes: 查询生成结果
     *
     * @Author: 玄尘
     * @Date: 2025/4/21 13:20
     * @param  string  $taskId
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function creations(string $taskId)
    {
        $path = "/ent/v2/tasks/{$taskId}/creations";
        return $this->get($path);
    }

    /**
     * Notes: 创建可控文生音效任务
     *
     * @Author: 玄尘
     * @Date: 2025/6/25
     * @param  \App\Models\PluginViduAudioDraw  $audioDraw
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function createTimingAudioTask(\App\Models\PluginViduAudioDraw $audioDraw)
    {
        $path = '/ent/v2/timing2audio';

        $data = [
            'model'          => $audioDraw->model,
            'duration'       => $audioDraw->duration,
            'timing_prompts' => $audioDraw->timing_prompts,
        ];

        if ($audioDraw->seed) {
            $data['seed'] = $audioDraw->seed;
        }

        $this->params = $data;
        return $this->post($path);
    }

    /**
     * Notes: 查询可控文生音效任务状态
     *
     * @Author: Claude 4.0 sonnet
     * @Date: 2025/6/25
     * @param  string  $taskId
     * @return \App\Packages\ViDu\ViduResponse
     */
    public function queryTimingAudioTask(string $taskId)
    {
        $path = "/ent/v2/tasks/{$taskId}/creations";
        return $this->get($path);
    }

}