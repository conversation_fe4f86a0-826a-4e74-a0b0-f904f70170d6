<?php

namespace App\Jobs\Vidu;

use App\Models\PluginViduAudioDraw;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ViduAudioQueryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected PluginViduAudioDraw $audioDraw;

    /**
     * Create a new job instance.
     */
    public function __construct(PluginViduAudioDraw $audioDraw)
    {
        $this->audioDraw = $audioDraw;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 检查任务是否还在处理中
            if ($this->audioDraw->canPolling()) {
                $this->audioDraw->audioQuery();
            }
        } catch (\Exception $e) {
            Log::error('Vidu可控音效查询任务失败', [
                'audio_draw_id' => $this->audioDraw->id,
                'task_id' => $this->audioDraw->task_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            $this->audioDraw->error('查询任务状态失败: ' . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Vidu可控音效查询任务彻底失败', [
            'audio_draw_id' => $this->audioDraw->id,
            'task_id' => $this->audioDraw->task_id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        $this->audioDraw->error('任务查询失败: ' . $exception->getMessage());
    }
}
